package com.stt.android.device.watchface.datasource

import com.stt.android.device.datasource.watchface.WatchFaceApi
import com.stt.android.device.domain.watchface.WatchFaceEntity
import com.suunto.connectivity.watchface.MdsWatchFace
import javax.inject.Inject

class WatchFaceDataSource @Inject constructor(
    private val watchFaceApi: WatchFaceApi,
) {
    suspend fun getInstalledWatchFaces(): List<MdsWatchFace> {
        return watchFaceApi.getInstalledWatchFaceList()
    }

    suspend fun setAsCurrentWatchface(watchFaceId: String) {
        watchFaceApi.setAsCurrentWatchFace(watchFaceId)
    }

    suspend fun installWatchFace(fileLocalPath: String, watchFace: WatchFaceEntity): Boolean {
        return watchFaceApi.startWatchFaceInstall(fileLocalPath, watchFace)
    }

    suspend fun uninstallWatchFace(watchFaceId: String) {
        watchFaceApi.uninstallWatchFace(watchFaceId)
    }
}
