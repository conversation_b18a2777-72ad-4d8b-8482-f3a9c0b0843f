package com.stt.android.device.datasource.watchface

import androidx.annotation.WorkerThread
import com.stt.android.device.CacheDirectory
import com.stt.android.utils.FileUtils
import timber.log.Timber
import java.io.File
import java.io.FileNotFoundException
import java.io.FileOutputStream
import java.security.MessageDigest
import java.util.Locale
import javax.inject.Inject

class WatchFaceFileStorageImpl @Inject constructor(
    @CacheDirectory private val cacheDirectory: File
) : WatchFaceFileStorage {

    private val watchfaceCacheDirectory = File(cacheDirectory, CACHE_SUBFOLDER_NAME)

    inner class StorageKey(
        val id: String,
        installCapability: String,
        installVersion: String
    ) {
        val key: String by lazy {
            with(MessageDigest.getInstance("SHA-256")) {
                update(id.encodeToByteArray())
                update(installCapability.encodeToByteArray())
                update(installVersion.encodeToByteArray())
                digest().toHexString()
            }
        }

        val filename: String by lazy {
            String.format(Locale.ROOT, FILENAME_FORMAT, key, FILE_SUFFIX_ZIP)
        }

        val file: File by lazy {
            File(watchfaceCacheDirectory, filename)
        }
    }

    @WorkerThread
    override fun existsInCache(
        id: String,
        installCapability: String,
        installVersion: String
    ): Boolean =
        StorageKey(id, installCapability, installVersion).file.exists()

    @WorkerThread
    override fun store(
        id: String,
        installCapability: String,
        installVersion: String,
        data: ByteArray
    ) {
        ensureCacheFolderExists()
        val file = StorageKey(id, installCapability, installVersion).file
        FileOutputStream(file)
            .use { output ->
                try {
                    output.write(data)
                    output.flush()
                    Timber.d("Wrote ${data.size} bytes successfully to ${file.absolutePath} (id=$id")
                } catch (e: Exception) {
                    // Do not leave partial files in the file system
                    val deleted = file.delete()
                    Timber.w(
                        e,
                        "Failed to store WatchFace file in cache, deletion success=$deleted"
                    )
                    throw e
                }
            }
    }

    override fun getAbsolutePath(
        id: String,
        installCapability: String,
        installVersion: String
    ): String {
        val file = StorageKey(id, installCapability, installVersion).file
        if (!file.exists()) {
            throw FileNotFoundException("getAbsolutePath: File not found for id: $id")
        }
        val filename = "$id.${FILE_SUFFIX_ZIP}"
        val workingFile = File(watchfaceCacheDirectory, filename)
        FileUtils.copyFile(file, workingFile, true)
        return workingFile.absolutePath
    }

    private fun ensureCacheFolderExists() {
        if (!watchfaceCacheDirectory.exists() && !watchfaceCacheDirectory.mkdirs()) {
            Timber.w("Failed to create cache directory ${watchfaceCacheDirectory.absolutePath}")
        }
    }

    companion object {
        private const val CACHE_SUBFOLDER_NAME = "watchface"
        private const val FILENAME_FORMAT = "watchface_cache_%s.%s"
        private const val FILE_SUFFIX_ZIP = "zip"
    }
}
