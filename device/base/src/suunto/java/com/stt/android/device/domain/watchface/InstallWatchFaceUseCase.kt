package com.stt.android.device.domain.watchface

import com.stt.android.device.datasource.watchface.WatchFaceDeviceApi
import com.stt.android.device.datasource.watchface.WatchFaceLocalDataSource
import com.stt.android.device.remote.watchface.WatchFaceRemoteDataSource
import javax.inject.Inject

class InstallWatchFaceUseCase @Inject constructor(
    private val remoteDataSource: WatchFaceRemoteDataSource,
    private val localDataSource: WatchFaceLocalDataSource,
    private val watchFaceDeviceApi: WatchFaceDeviceApi,
) {
    suspend fun startInstall(id: String, localPath: String) {
        val watchFace = localDataSource.findById(id)
//        watchFaceDeviceApi.startWatchFaceInstall()
    }
}
