package com.stt.android.device.datasource.watchface

import androidx.annotation.WorkerThread

interface WatchFaceFileStorage {

    @WorkerThread
    fun existsInCache(
        id: String,
        installCapability: String,
        installVersion: String
    ): Bo<PERSON>an

    @WorkerThread
    fun store(
        id: String,
        installCapability: String,
        installVersion: String,
        data: ByteArray
    )

    fun getAbsolutePath(
        id: String,
        installCapability: String,
        installVersion: String
    ): String
}
