package com.stt.android.data.source.local.watchface

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.TypeConverter
import androidx.room.TypeConverters
import com.squareup.moshi.JsonClass
import com.stt.android.data.source.local.TABLE_WATCH_FACE_DEVICE_STATUS
import com.stt.android.data.source.local.watchface.LocalWatchFaceDeviceStatus.DbFields.ID
import com.stt.android.data.source.local.watchface.LocalWatchFaceDeviceStatus.DbFields.WATCH_SERIAL

@Entity(
    tableName = TABLE_WATCH_FACE_DEVICE_STATUS,
    primaryKeys = [WATCH_SERIAL, ID]
)
@TypeConverters(WatchFaceStatusConverter::class)
data class LocalWatchFaceDeviceStatus(
    @ColumnInfo(name = WATCH_SERIAL) val watchSerial: String,
    @ColumnInfo(name = ID) val id: String,
    @ColumnInfo(name = WATCH_FACE_PRE_IMG_NAME) val preImgName: String,
    @ColumnInfo(name = INSTALL_CAPABILITY) val installCapability: String,
    @ColumnInfo(name = INSTALL_VERSION) val installVersion: String,
    @ColumnInfo(name = FILE_SIZE) val fileSize: Long,
    @ColumnInfo(name = FILE_MD5) val fileMd5: String,
    @ColumnInfo(name = STATUS) val status: LocalWatchFaceStatus,
) {
    companion object DbFields {
        const val WATCH_SERIAL = "serial"
        const val ID = "id"
        const val STATUS = "status"
        const val INSTALL_CAPABILITY = "capability"
        const val INSTALL_VERSION = "version"
        const val WATCH_FACE_PRE_IMG_NAME = "preImgName"
        const val FILE_SIZE = "size"
        const val FILE_MD5 = "md5"
    }
}

@JsonClass(generateAdapter = false)
enum class LocalWatchFaceStatus {
    UNKNOWN,
    NOT_SUPPORTED, // Backend reported as not supported when trying to download file
    DOWNLOADING, // file is being downloaded or waiting for downloading to start
    DOWNLOADED, // file downloaded successfully
    INSTALLING, // file is being uploaded to the watch
    IN_WATCH, // Successfully synced to watch
    WATCH_FULL; // WatchFace did not fit in watch
}

class WatchFaceStatusConverter {
    @TypeConverter
    fun fromWatchFaceStatus(value: LocalWatchFaceStatus): String = value.name

    @TypeConverter
    fun toWatchFaceStatus(value: String): LocalWatchFaceStatus {
        return LocalWatchFaceStatus.valueOf(value)
    }
}
